import React, { useEffect } from 'react'
import <PERSON>actDOMServer from 'react-dom/server'
import FormPDFLayout from './App'
import html2pdf from 'html2pdf.js'

const Hello = ({
  weather,
  imageBase64,
  formattedResponses,
  name,
  oppData,
  createdBy,
}: any) => {
  useEffect(() => {
    ;(async () => {
      try {
        // 1️⃣ Convert HTML to PDF Blob
        const htmlContent = ReactDOMServer.renderToString(
          <FormPDFLayout
            weather={weather?.data}
            imageBase64={imageBase64}
            formattedResponses={formattedResponses}
            // name={formDataById?.name}
            oppData={oppData}
            // createdBy={currentMember?.name}
          />
        )

        const element = document.createElement('div')
        element.innerHTML = `
      <style>
      * { word-wrap: break-word; }
      table, div { page-break-inside: avoid; }
      p { page-break-inside: avoid; word-break: break-word; }
      
      /* Ensure sections don’t split */
      .avoid-break {
        page-break-before: always; 
        page-break-inside: avoid;
      }
    </style>
    ${htmlContent}
  `

        element.innerHTML = htmlContent

        const pdfBlob = await html2pdf()
          .set({
            margin: 8.5, // Explicit margins [top, right, bottom, left]
            filename: 'document.pdf',
            image: { type: 'jpeg', quality: 1.0 },
            html2canvas: {
              scale: 2, // Reduced from 2 to prevent scaling overflow
              useCORS: true,
            },
            jsPDF: {
              unit: 'mm',
              format: 'letter',
              orientation: 'portrait',
            },
            pagebreak: { mode: ['avoid', 'css'] },
            // margin: 10,
            // filename: 'report.pdf',
            // image: { type: 'jpeg', quality: 0.98 },
            // html2canvas: { scale: 2, useCORS: true },
            // jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' },
            // pagebreak: { mode: ['avoid-all', 'css'] },
          })
          .from(htmlContent)
          .outputPdf('blob')

        console.log('TEST===[log]===>', {
          url: URL.createObjectURL(pdfBlob),
        })
      } catch (error) {
        console.log('TEST===[error]===>', error)
      }
    })()
  }, [])
  return <div>Hello</div>
}

export default Hello
