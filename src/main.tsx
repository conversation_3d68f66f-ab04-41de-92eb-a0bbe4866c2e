import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'

const props = {
  imageBase64: null,
  formattedResponses: [
    {
      type: 'date',
      required: true,
      label: 'Date',
      className: 'form-control',
      name: 'date-1746903448645-0',
      subtype: 'date',
      order: 1,
      value: '2009-10-09',
    },
    {
      type: 'location',
      required: false,
      label: 'Location',
      name: 'location-1746903444753-0',
      value: 'No location information available',
      order: 2,
    },
    {
      type: 'autocomplete',
      required: true,
      label: 'Vehicle',
      className: 'form-control',
      name: 'autocomplete-1746903473665-0',
      requireValidOption: false,
      values: [
        {
          label: '1998 F150',
        },
        {
          label: '2000 F250',
        },
        {
          label: '2001 Toyota Tundra',
        },
        {
          label: '2005 Chevy 2500 HD',
        },
        {
          label: '2004 F150',
        },
        {
          label: '2018 F150',
        },
        {
          label: '2011 F150',
        },
        {
          label: '2016 GMC 2500 HD',
        },
        {
          label: '2013 F150',
        },
        {
          label: '2014 F150',
        },
        {
          label: 'Tesla Standard',
        },
        {
          label: 'Tesla Dual Motor',
        },
        {
          label: '2017 Promaster Van',
        },
        {
          label: 'Soccer Mom Van',
        },
      ],
      order: 3,
      value: 'Sit id qui consequa',
    },
    {
      type: 'header',
      subtype: 'h2',
      label: 'Interior Inspection',
      order: 4,
      value: '',
    },
    {
      type: 'number',
      required: true,
      label: 'Vehicle Mileage',
      className: 'form-control',
      name: 'number-1746903601789-0',
      subtype: 'number',
      order: 5,
      value: 410,
    },
    {
      type: 'file',
      required: true,
      label: 'Interior photos front & back seat',
      className: 'form-control',
      name: 'file-1746903633085-0',
      accept: 'image/*',
      multiple: true,
      order: 6,
      value: [
        {
          _id: '1400b686-4a38-076c-3929-8742a53f796e',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755928988793-file_example_JPG_100kB__Copy_15_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746903633085-0',
        },
      ],
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Seat Belt',
      inline: true,
      name: 'radio-group-1746903683883-0',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 7,
      value: 'Pass',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Heater, Defroster, A/C',
      inline: true,
      name: 'radio-group-1746903751070',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 8,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Dash Warning Lights',
      inline: true,
      name: 'radio-group-1746903764028',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 9,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Horn',
      inline: true,
      name: 'radio-group-1746903781393',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 10,
      value: 'Pass',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Mirrors',
      inline: true,
      name: 'radio-group-1746903791125',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 11,
      value: 'Pass',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Window Cracks',
      inline: true,
      name: 'radio-group-1746903808100',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 12,
      value: 'Pass',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Fire Extinguisher',
      inline: true,
      name: 'radio-group-1746903821272',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 13,
      value: 'Pass',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Cab Light',
      inline: true,
      name: 'radio-group-1746904178421',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 14,
      value: 'Pass',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Wipers',
      inline: true,
      name: 'radio-group-1746904184759',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 15,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'First Aid Kit',
      inline: true,
      name: 'radio-group-1746904190725',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 16,
      value: 'Pass',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Door Locks',
      inline: true,
      name: 'radio-group-1746904227251',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 17,
      value: 'Fail',
    },
    {
      type: 'textarea',
      required: false,
      label: 'Notes on Interior Failed Items',
      className: 'form-control',
      name: 'textarea-1746904251900-0',
      subtype: 'textarea',
      order: 18,
      value: 'Officiis temporibus ',
    },
    {
      type: 'file',
      required: false,
      label: 'Photos of Interior Failed Items',
      className: 'form-control',
      name: 'file-1746905483702-0',
      accept: 'image/*',
      multiple: true,
      order: 19,
      value: [],
    },
    {
      type: 'header',
      subtype: 'h3',
      label: 'Under the Hood Inspection',
      order: 20,
      value: '',
    },
    {
      type: 'file',
      required: true,
      label: 'Under the Hood Photo',
      className: 'form-control',
      name: 'file-1746905462069-0',
      accept: 'image/*',
      multiple: false,
      order: 21,
      value: [
        {
          _id: 'dfa620b1-1aef-f94f-025b-27e7598ea5f0',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755928988793-file_example_JPG_100kB__Copy_12_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905462069-0',
        },
      ],
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Fan Belt (cracks, wear)',
      inline: true,
      name: 'radio-group-1746904236590',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 22,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Radiator Hoses (cracks, leaks)',
      inline: true,
      name: 'radio-group-1746904466368',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 23,
      value: 'Pass',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Battery (terminals, connections)',
      inline: true,
      name: 'radio-group-1746904483903',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 24,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Air Filter',
      inline: true,
      name: 'radio-group-1746904498379',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 25,
      value: 'Pass',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Transmission Fluid',
      inline: true,
      name: 'radio-group-1746904546199',
      other: false,
      values: [
        {
          label: 'Full',
        },
        {
          label: 'Low',
        },
      ],
      order: 26,
      value: 'Low',
    },
    {
      type: 'text',
      required: false,
      label: 'If Low, how much added?',
      className: 'form-control',
      name: 'text-1746904569772-0',
      subtype: 'text',
      order: 27,
      value: 'Aut libero sunt aute',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Engine Oil',
      inline: true,
      name: 'radio-group-1746904565541',
      other: false,
      values: [
        {
          label: 'Full',
        },
        {
          label: 'Low',
        },
      ],
      order: 28,
      value: 'Full',
    },
    {
      type: 'text',
      required: false,
      label: 'If Low, how much added?',
      className: 'form-control',
      name: 'text-1746904681277',
      subtype: 'text',
      order: 29,
      value: 'Dolor tempore vel n',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Brake Fluid',
      inline: true,
      name: 'radio-group-1746904677141',
      other: false,
      values: [
        {
          label: 'Full',
        },
        {
          label: 'Low',
        },
      ],
      order: 30,
      value: 'Full',
    },
    {
      type: 'text',
      required: false,
      label: 'If Low, how much added?',
      className: 'form-control',
      name: 'text-1746904600761',
      subtype: 'text',
      order: 31,
      value: 'Cupidatat minus impe',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Windshield Wiper Fluid',
      inline: true,
      name: 'radio-group-1746904704547',
      other: false,
      values: [
        {
          label: 'Full',
        },
        {
          label: 'Low',
        },
      ],
      order: 32,
      value: 'Low',
    },
    {
      type: 'text',
      required: false,
      label: 'If Low, how much added?',
      className: 'form-control',
      name: 'text-1746904848739',
      subtype: 'text',
      order: 33,
      value: 'Qui velit deserunt p',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Power Steering Fluid',
      inline: true,
      name: 'radio-group-1746904837041',
      other: false,
      values: [
        {
          label: 'Full',
        },
        {
          label: 'Low',
        },
      ],
      order: 34,
      value: 'Full',
    },
    {
      type: 'text',
      required: false,
      label: 'If Low, how much added?',
      className: 'form-control',
      name: 'text-1746904848211',
      subtype: 'text',
      order: 35,
      value: 'Eligendi et illum q',
    },
    {
      type: 'header',
      subtype: 'h3',
      label: 'Exterior Inspection',
      order: 36,
      value: '',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Headlights & Brights',
      inline: true,
      name: 'radio-group-1746905001785',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 37,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Turn Signals',
      inline: true,
      name: 'radio-group-1746905030317',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 38,
      value: 'Pass',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Brake Lights',
      inline: true,
      name: 'radio-group-1746905036549',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 39,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Reverse Lights',
      inline: true,
      name: 'radio-group-1746905044521',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 40,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Running Lights',
      inline: true,
      name: 'radio-group-1746905051499',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 41,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Wheels - Lugs Tight',
      inline: true,
      name: 'radio-group-1746905055883',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 42,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Tire Pressure',
      inline: true,
      name: 'radio-group-1746905072018',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 43,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Tire Condition',
      inline: true,
      name: 'radio-group-1746905078394',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 44,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Trailer Hitch',
      inline: true,
      name: 'radio-group-1746905086194',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 45,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Body Damage',
      inline: true,
      name: 'radio-group-1746905148121',
      other: false,
      values: [
        {
          label: 'New',
        },
        {
          label: 'Existing',
        },
        {
          label: 'None',
        },
      ],
      order: 46,
      value: 'Existing',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Fluid Leaks Under Vehicle',
      inline: true,
      name: 'radio-group-1746905217874',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 47,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Brake Pads',
      inline: true,
      name: 'radio-group-1746905238018',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 48,
      value: 'Pass',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Shock Absorbers',
      inline: true,
      name: 'radio-group-1746905243914',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 49,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'U-Joints on Drive Shafts',
      inline: true,
      name: 'radio-group-1746905250563',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 50,
      value: 'Fail',
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Logos & Lettering',
      inline: true,
      name: 'radio-group-1746905269038',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 51,
      value: 'Pass',
    },
    {
      type: 'file',
      required: true,
      label: 'Photos of all 4 sides',
      className: 'form-control',
      name: 'file-1746905400838-0',
      accept: 'image/*',
      multiple: true,
      order: 52,
      value: [
        {
          _id: '43986f0e-ec5b-1a7b-0fe7-b91c01ce4918',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755928988793-file_example_JPG_100kB__Copy_12_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905400838-0',
        },
      ],
    },
    {
      type: 'radio-group',
      required: true,
      label: 'Wash Outside',
      inline: true,
      name: 'radio-group-1746905281278',
      other: false,
      values: [
        {
          label: 'Pass',
        },
        {
          label: 'Fail',
        },
      ],
      order: 53,
      value: 'Fail',
    },
    {
      type: 'textarea',
      required: false,
      label: 'Notes on Exterior Failed Items',
      className: 'form-control',
      name: 'textarea-1746905325434-0',
      subtype: 'textarea',
      order: 54,
      value: 'Ut enim obcaecati ex',
    },
    {
      type: 'file',
      required: false,
      label: 'Pictures of Failed Exterior Items',
      className: 'form-control',
      name: 'file-1746905326160-0',
      accept: 'image/*',
      multiple: true,
      order: 55,
      value: [
        {
          _id: '99c4c47c-14a0-0e03-33ab-e2a83147573a',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755928988793-file_example_JPG_100kB__Copy_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: '26b65821-204e-cd2b-068a-9eafc65e769a',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755928988793-file_example_JPG_100kB__Copy_5_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: '9a9125ed-da11-12d7-bb2d-bc3fefd9902b',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755928988793-file_example_JPG_100kB__Copy_2_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: '8593d4f6-a6db-4d0c-849a-91edf369f3be',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755928988793-file_example_JPG_100kB__Copy_6_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: '2024aab7-a146-fda4-14cf-a77b6b7d1b01',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755928988793-file_example_JPG_100kB__Copy_4_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: '5c26b20a-fd41-c12a-bfbe-6e05ad54fef2',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755928988793-file_example_JPG_100kB__Copy_7_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: '9ac5c3da-76fc-b71e-afed-ca91415c52f7',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755928988793-file_example_JPG_100kB__Copy_9_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: '5b4f5f85-43e7-0e04-ce13-92a3137c858c',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755928988793-file_example_JPG_100kB__Copy_8_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: '9c4c6583-9806-2135-5431-3c1ceaab24ac',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755928988793-file_example_JPG_100kB__Copy_3_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: 'b11edba4-a79c-5bbe-2a25-a7d515ffe536',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755928988793-file_example_JPG_100kB__Copy_10_.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
        {
          _id: '75ade165-4d57-c876-4524-8c853bdcfc1d',
          mimetype: 'image/jpeg',
          url: 'https://nhrdev.s3.us-west-1.amazonaws.com/companies/0f33b070-a7f2-43f3-8d07-54fdfd4378e3/member/3eb21540-9e04-4d7d-9dc5-63a44b955151/image/2025/08/1755928988793-file_example_JPG_100kB.jpeg',
          tags: ['Vehicle Inspection Form'],
          formFieldByName: 'file-1746905326160-0',
        },
      ],
    },
    {
      type: 'file',
      required: false,
      label: 'Video of Failed Item (if needed)',
      className: 'form-control',
      name: 'file-1746905557206-0',
      accept: 'video/*',
      multiple: true,
      order: 56,
      value: [],
    },
  ],
  name: 'Vehicle Inspection Form',
  createdBy: 'vipin yadav',
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App {...props} />
  </StrictMode>
)
