.form-container {
  display: flex;
  justify-content: center;
  align-items: start;
  padding: 20px;
  height: 100vh;
}
.form-sheet {
  width: 50%;
  background: #eae8e8;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
.form-content {
  margin: 20px;
}
.form-group {
  display: flex;
  justify-content: space-between;
}
.form-label {
  width: 40%;
}
.form-input {
  /* width: 50%; */
  padding: 5px;
}
.submit-button {
  width: 100%;
  padding: 10px;
  background: #007bff;
  color: white;
  border: none;
}
.error {
  color: red;
  font-size: 12px;
  text-align: end;
  margin-top: 5px;
}

.field-wrapper {
  display: flex;
  flex-direction: column;
  width: 60%;
}

.header-field {
  width: 100%;
}

.file-container {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.file-item {
  height: 50px;
  width: 50px;
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  position: relative;
}

.file-preview {
  width: 50px;
  height: 50px;
}

.file-name {
  font-size: 14px;
}

.new-file-tag {
  background-color: red;
  color: white;
  font-size: 10px;
  padding: 2px 5px;
  border-radius: 4px;
  position: absolute;
  top: -5px;
  right: -5px;
}

.media-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
}
.fileName {
  display: flex;
  padding: 0 4px;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  position: absolute;
  bottom: 0px;
  width: 100%;
  background-color: grey;
  span {
    color: white;
  }
}
.media-item {
  position: relative;
  width: 190px;
  height: 190px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px solid #ccc;
  border-radius: 8px;
  overflow: hidden;
}

.media-content {
  width: 100%;
  object-fit: cover;
}

.checkbox-container {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(255, 255, 255, 0.8);
  padding: 5px;
  border-radius: 4px;
}

/* ✅ Mobile Responsive (less than 800px) */
@media screen and (max-width: 800px) {
  .form-container {
    flex-direction: column;
    align-items: center;
    height: auto;
    padding: 10px;
  }

  .form-sheet {
    width: 100%;
  }

  .form-content {
    margin: 10px;
  }

  .form-group {
    flex-direction: column;
    gap: 10px;
  }

  .form-label {
    width: 100%;
  }

  .form-input {
    width: 100%;
  }

  .field-wrapper {
    width: 100%;
  }

  .media-item {
    width: 100%;
    height: auto;
  }
}

.camera-file-upload {
  display: flex;
  align-items: center;
  gap: 1rem;
}
.file-wrap {
  display: flex;
  align-items: baseline;
  gap: 1rem;
}
.camera-button {
  background-color: #b2d9ff;
  border: none;
  height: 40px;
  width: 40px;
  border-radius: 10%;
  padding: 0.4rem;
  cursor: pointer;

  transition: background 0.2s;
}

.camera-button:hover {
  background-color: #7a91a8;
}

/* PDF */

.inspection-report {
  --family: sans-serif;
  --fontSize: 14px;
  font-family: var(--family);
  line-height: 1.6;
  /* max-width: 800px; */
  margin: 0 auto;
  /* padding: 20px; */
}

.no-image {
  font-family: var(--family);
}

.logo {
  width: 300px;
}
.inspection-header {
  text-align: left;
  max-width: 50%;
  text-transform: capitalize;

  h2 {
    font-family: var(--family);
    line-height: 1.3;
  }
  p {
    font-family: var(--family);
    text-transform: capitalize;
    line-height: 1.3;
    font-size: 14px;
  }
}
.dual-column-title {
  font-size: var(--fontSize);
  font-weight: 700;
  /* margin-bottom: 4px; */
  font-family: var(--family);
}
.section-title {
  /* font-size: 18px; */
  /* font-weight: 700; */
  margin: 16px 0px;
  text-align: center;
  padding: 4px;
  font-family: var(--family);
  text-transform: uppercase;
  background-color: #e7e7e7;
}
.dual-column {
  display: flex;
  justify-content: space-between;
  margin: 10px 0px;
}
.column {
  width: 48%;
}
.client-group {
  margin-bottom: 20px;

  div {
    font-size: var(--fontSize);
    font-family: var(--family);
    line-height: 1.3;
  }
}

.field-group {
  margin-top: 20px;
  display: flex;
  gap: 10px;
  font-size: 14px;

  .field-label {
    width: 350px;
    font-weight: bold;
    flex-shrink: 0;
    position: relative;
    display: flex;
    align-items: center;
    &::after {
      content: '';
      flex-grow: 1;
      height: 1px;
      margin-left: 10px;
      padding-top: 10px;
      border-bottom: 1px solid rgba(106, 116, 126, 0.2);
    }
  }
}

/* .field-label {
  width: 350px;
  font-weight: bold;
  flex-shrink: 0;
} */

.field-label {
  width: 350px;
  font-weight: bold;
  flex-shrink: 0;
  position: relative;
  display: flex;
  align-items: center;

  font-family: var(--family);
}

.header-field {
  font-family: var(--family);
}

.field-value {
  flex-grow: 1;
  word-break: break-word;
  font-family: var(--family);
}
.signature-line {
  border-top: 1px solid #000;
  margin-top: 40px;
  padding-top: 10px;
}
.margin-top {
  margin-top: 10px;
}

.grid-layout {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/*.uploaded-image {
  width: 200px;
  height: 150px;
  /~ max-width: 100%;
    height: auto; ~/
  object-fit: contain;
  /~ border-radius: 8px; ~/
}
*/

.file-links {
  width: 100%;
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.file-link {
  color: #1362fe;
  text-decoration: underline;
  word-break: break-all;
}
/* Page break prevention rules */
img {
  page-break-inside: avoid;
  break-inside: avoid;
}

.no-break {
  break-inside: avoid;
  page-break-inside: avoid;
  overflow: hidden;
}

/* Prevent content splitting across pages */
.inspection-report {
  page-break-inside: avoid;
  break-inside: avoid;
}

.section-title {
  page-break-after: avoid;
  break-after: avoid;
  /* Keep section title with following content */
  orphans: 3;
  widows: 3;
}

.field-group,
.field-group-col {
  page-break-inside: avoid;
  break-inside: avoid;
}

.client-group {
  page-break-inside: avoid;
  break-inside: avoid;
}

.dual-column {
  page-break-inside: avoid;
  break-inside: avoid;
}

.inspection-header {
  page-break-after: avoid;
  break-after: avoid;
}

.grid-layout {
  page-break-inside: avoid;
  break-inside: avoid;
  /* Ensure the entire grid stays together */
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  /* Force the grid to stay as one unit */
  overflow: hidden;
  /* Prevent any breaking within the grid */
  page-break-before: auto;
  page-break-after: auto;
}

.file-links {
  page-break-inside: avoid;
  break-inside: avoid;
}

.signature-line {
  page-break-before: avoid;
  break-before: avoid;
}

/* Enhanced protection for file upload sections */
.field-group-col {
  page-break-inside: avoid;
  break-inside: avoid;
  /* Keep the entire file section together */
  margin-top: 20px;
  display: flex;
  font-size: var(--fontSize);
  flex-direction: column;
}

/* Ensure uploaded images stay with their container */
.uploaded-image {
  page-break-inside: avoid;
  break-inside: avoid;
  width: 200px;
  object-fit: contain;
}

/* Special handling for large image sections */
.field-group-col:has(.grid-layout .uploaded-image) {
  page-break-before: auto;
  page-break-inside: avoid;
  break-inside: avoid;
  /* If the section is too large, start on new page */
  min-height: 0;
}

/* Alternative approach - force large sections to new page if needed */
.large-image-section {
  page-break-before: auto;
  page-break-inside: avoid !important;
  break-inside: avoid !important;
  page-break-after: auto;
  /* Force the entire section to be treated as one unit */
  display: block;
  overflow: hidden;
}

/* Ensure large sections start fresh if they don't fit */
.large-image-section .field-label {
  page-break-after: avoid !important;
  break-after: avoid !important;
}

.large-image-section .grid-layout {
  page-break-inside: avoid !important;
  break-inside: avoid !important;
  /* Keep all images together */
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* Print-specific rules for better page break control */
@media print {
  /* Force each major section to start on a new page if it has many images */
  .large-image-section {
    page-break-before: always !important;
    page-break-inside: avoid !important;
    break-inside: avoid !important;
    page-break-after: auto !important;
    display: block !important;
    width: 100% !important;
    min-height: 50vh !important;
  }

  /* For smaller sections, just avoid breaking */
  .field-group-col:not(.large-image-section) {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
  }

  .grid-layout {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
    display: block !important;
  }

  .section-title {
    page-break-after: avoid !important;
    break-after: avoid !important;
    page-break-before: auto !important;
    orphans: 3;
    widows: 3;
  }

  /* Force images to display in a more print-friendly way */
  .uploaded-image {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
    display: inline-block !important;
    margin: 5px !important;
  }
}

.flex {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid rgba(106, 116, 126, 0.2);
  padding-bottom: 4px;
}
